import os
from task.base_activity import <PERSON><PERSON><PERSON><PERSON><PERSON>
from task.base_activity import <PERSON><PERSON><PERSON>ger
from task.base_activity import <PERSON><PERSON>eader
from task.base_activity import PSFileHandler
import win32com.client as win

def execute_jsx(script):
    photoshop = win.Dispatch("Photoshop.Application")
    result = photoshop.DoJavaScript(script)
    return result

def get_layer_group_names(layer_set):
    names = []
    for sub_layer_set in layer_set.layerSets:
        sub_layer_name = sub_layer_set.name
        names.append(sub_layer_name)
        names.extend(get_layer_group_names(sub_layer_set))
    return names

def generate_jsx_script(substring_to_match, hidden_layer_names, shown_layer_names):
    return f"""
    var doc = app.activeDocument;

    function get_layer_group_names(layer_set) {{
        var names = [];
        for (var i = 0; i < layer_set.layerSets.length; i++) {{
            var sub_layer_set = layer_set.layerSets[i];
            var sub_layer_name = sub_layer_set.name.replace(/\\d+/g, '');  // 去掉数字部分

            // 检查图层名称是否应该隐藏
            if ({' || '.join(f'sub_layer_name === "{name}"' for name in hidden_layer_names)}) {{
                sub_layer_set.visible = false;  // 设置图层组为隐藏
            }}

            // 检查图层名称是否应该显示
            if ({' || '.join(f'sub_layer_name === "{name}"' for name in shown_layer_names)}) {{
                sub_layer_set.visible = true;  // 设置图层组为显示
            }}

            names.push(sub_layer_name);
            names = names.concat(get_layer_group_names(sub_layer_set));
        }}
        return names;
    }}

    var layerGroupNames = [];

    for (var i = 0; i < doc.layerSets.length; i++) {{
        var layerSet = doc.layerSets[i];
        var groupName = layerSet.name.replace(/\\d+/g, '');  // 去掉数字部分

        // 检查图层组名称是否应该隐藏
        if ({' || '.join(f'groupName === "{name}"' for name in hidden_layer_names)}) {{
            layerSet.visible = false;  // 设置图层组为隐藏
        }}

        // 检查图层组名称是否应该显示
        if ({' || '.join(f'groupName === "{name}"' for name in shown_layer_names)}) {{
            layerSet.visible = true;  // 设置图层组为显示
        }}

        layerGroupNames.push(groupName);
        layerGroupNames = layerGroupNames.concat(get_layer_group_names(layerSet));
    }}

    layerGroupNames.join(',');
    """

def run(base_folder, excel_file):
    img_list = ['黑色+绳黑金', '草紫+绳紫彩虹', '米白+绳白灰', '白色+绳白灰', '粉色+绳粉黑', '草绿+绳草绿', '米白+绳米白', '草紫+绳草紫', '浅紫+绳浅紫', '黑色+绳黑', '红色+绳红', '粉色+绳玫瑰金', '粉色+绳浅粉']

    # 初始化文件操作对象
    file_handle = DirectoryReader()
    # 初始化日志对象
    logger = BaseLogger()
    # 初始化psd文件处理对象
    ps_file_handle = PSFileHandler()
    # 初始化表格对象
    excel_handler = ExcelHandler(excel_file)
    
    # 打开表格
    excel_obj = excel_handler.open_excel()
    
    # 读取表格文件路径
    psd_config = excel_handler.read_sheet_data(excel_obj, "文件路径")

    # 使用与手机壳2个装套图.py相同的路径处理方式
    psd_conf = {
        "psd_folder": psd_config[0][1],
        "tif_folder": psd_config[1][1],
        "result_folder": psd_config[2][1]
    }


    # # 读取任务表格数据
    psd_data = excel_handler.read_sheet_data(excel_obj, "图案型号文件名")
    
    logger.log_info(psd_conf)
    result_col_index = 0
    # 当前表格行
    # # 循环遍历数据
    for index, row in enumerate(psd_data):
        if index == 0:
            if "运行结果" in [cell.value for cell in excel_obj.Sheets("图案型号文件名").Rows(1)]:
                # 获取 "运行结果" 列的索引
                result_col_index = [cell.value for cell in excel_obj.Sheets("图案型号文件名").Rows(1)].index("运行结果") + 1
                # 清空该列内容，保留第一行表头
                for row_idx in range(2, excel_obj.Sheets("图案型号文件名").UsedRange.Rows.Count + 1):
                    excel_handler.write_to_cell(excel_obj, "图案型号文件名", row_idx, result_col_index, "")
            else:
                excel_handler.write_to_cell(excel_obj, "图案型号文件名", 1, len(row) + 1, f"运行结果")
                result_col_index = len(row) + 1
            continue
        current_num = index + 1
        logger.log_info(f"行数: {current_num}, 数据: {row}")
        # 判断路径是否存在
        result_folder = os.path.join(psd_conf["result_folder"], row[2] + row[3])

        if not file_handle.ensure_folder_exists(result_folder):
            logger.log_error(f"路径不存在: {result_folder}")
            excel_handler.write_to_cell(excel_obj, "图案型号文件名", current_num, result_col_index, f"失败-路径不存在: {result_folder}")
            continue
        else:
            logger.log_info(f"成品路径正常: {result_folder}")
        psd_file = os.path.join(psd_conf["psd_folder"], row[1])
        logger.log_info(f"psd文件路径: {psd_file}")

        # 使用更可靠的网络路径检查方法
        if not file_handle.check_network_path_exists(psd_file):
            logger.log_error(f"psd文件路径不存在: {psd_file}")
            excel_handler.write_to_cell(excel_obj, "图案型号文件名", current_num, result_col_index, f"失败-psd文件路径不存在: {psd_file}")
            # ps_file_handle.close_ps_file()
            continue
        # 打开psd文件
        tif_file = os.path.join(psd_conf["tif_folder"], row[0])

        # 使用更可靠的网络路径检查方法
        if not file_handle.check_network_path_exists(tif_file):
            logger.log_error(f"tif文件路径不存在: {tif_file}")
            excel_handler.write_to_cell(excel_obj, "图案型号文件名", current_num, result_col_index, f"失败-tif文件路径不存在: {tif_file}")
            ps_file_handle.close_ps_file()
            continue
        try:
            ps_file_handle.open_ps_file(psd_file)

           
            
            color_config = row[4]

            logger.log_info(f"tif文件路径: {tif_file}")

            replace_one = "无内容"
            

            logger.log_info(f"颜色配置: {color_config}")
            

            # 定义要执行的 JSX 脚本
            substring_to_match = "替换内容1"  # Replace with the substring you want to match

            jsx_script = f"""
            var doc = app.activeDocument;
            var matchedLayerNames = [];

            function check_and_add_name(layerName) {{
                if (layerName.indexOf("{substring_to_match}") !== -1) {{
                    matchedLayerNames.push(layerName);
                }}
            }}

            function get_layer_group_names(layer_set) {{
                for (var i = 0; i < layer_set.layers.length; i++) {{
                    check_and_add_name(layer_set.layers[i].name);
                }}

                for (var i = 0; i < layer_set.layerSets.length; i++) {{
                    var sub_layer_set = layer_set.layerSets[i];
                    check_and_add_name(sub_layer_set.name);
                    get_layer_group_names(sub_layer_set);
                }}
            }}

            for (var i = 0; i < doc.layers.length; i++) {{
                check_and_add_name(doc.layers[i].name);
            }}

            for (var i = 0; i < doc.layerSets.length; i++) {{
                check_and_add_name(doc.layerSets[i].name);
                get_layer_group_names(doc.layerSets[i]);
            }}

            matchedLayerNames.join(',');
            """

            # 执行 JSX 脚本
            result = execute_jsx(jsx_script)

            # 将结果字符串转换为 Python 列表
            matched_layer_names = result.split(',')
            print(matched_layer_names)

            # 打印匹配的图层名称
            for name in matched_layer_names:
                if "替换内容1" == name.replace(" ", ""):
                    replace_one = name
            
            if replace_one == "无内容":
                logger.log_error(f"没有找到匹配的图层: {replace_one}")
                continue
            else:   
                logger.log_info(f"找到匹配的图层: {replace_one}")
                # 替换图层名称
                ps_file_handle.replace_img(replace_one, tif_file)
                logger.log_info(f"替换图层名称: {replace_one} -> {row[3]}")

            # 从 img_list 中移除与 color_config 一致的项
            hidden_layer_names = img_list
            if color_config in hidden_layer_names:
                hidden_layer_names.remove(color_config)
            shown_layer_names = [color_config]

            # 定义 JSX 脚本
            jsx_script = generate_jsx_script(substring_to_match, hidden_layer_names, shown_layer_names)

            # 执行 JSX 脚本
            result = execute_jsx(jsx_script)

            file_list = excel_handler.read_sheet_data(excel_obj, "文件夹图层名")

            hide_layers = []
            for file_item in file_list:
                hide_layers.append(file_item[0])

            ps_file_handle.hide_all_result_layers(app=win.Dispatch("Photoshop.Application"), layer_names=hide_layers)
            for file_item in file_list:
                
                export_file_path = result_folder + "\\" + row[2] + row[3] + "-" + file_item[0] + ".jpg"
                logger.log_info(f"导出图层={file_item[0]} ==> {export_file_path}")
                ps_file_handle.export_psd_to_jpg_with_quality(app=win.Dispatch("Photoshop.Application"), export_file_path=export_file_path, layer_name=file_item[0])
            excel_handler.write_to_cell(excel_obj, "图案型号文件名", current_num, result_col_index, "成功")
            if psd_data[index+1][1] == psd_file:
                logger.log_info(f"文件路径相同,跳过当前操作")
                ps_file_handle.recover_init()
            else:
                logger.log_info(f"文件路径不同,关闭当前文件")
                ps_file_handle.close_ps_file()
        except Exception as e:
            logger.log_error(f"处理文件时出错: {e}")
            excel_handler.write_to_cell(excel_obj, "图案型号文件名", current_num, result_col_index, f"失败-处理文件时出错: {e}")
            ps_file_handle.close_ps_file()
            continue
        
    excel_handler.close_excel(excel_obj)


